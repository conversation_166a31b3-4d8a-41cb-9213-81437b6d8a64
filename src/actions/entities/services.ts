"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { DeviceType } from "@prisma/client";
import { ServiceStatus } from "@/components/pages/dashboard/services/types";

// Import the service schema from the components
import { EnhancedServiceSchema } from "@/components/pages/dashboard/services/new/types";
import { canCreateTransaction } from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";

// Function to get the next service number with global uniqueness and atomic generation
export const getNextServiceNumber = async (date?: Date | string) => {
  try {
    // Use provided date or current date
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();
    const yearSuffix = String(year).slice(-2); // Get last 2 digits of year

    // Service number format: SRV-{YY}S{NNNNNN}
    const prefix = "SRV";
    const regexPattern = new RegExp(`^${prefix}-${yearSuffix}S\\d{6}$`);

    // Use a transaction to ensure atomicity
    const result = await db.$transaction(async (tx) => {
      // Find all services with auto-generated numbers for the current year
      const servicesWithAutoNumbers = await tx.service.findMany({
        where: {
          serviceNumber: {
            startsWith: `${prefix}-${yearSuffix}S`,
          },
        },
        select: {
          serviceNumber: true,
        },
      });

      // Extract numbers and find the maximum
      let maxNumber = 0;
      for (const service of servicesWithAutoNumbers) {
        // Extract the number part after 'S' (e.g., SRV-25S000001 -> 000001)
        const numberPart = service.serviceNumber.split("S")[1];
        if (numberPart) {
          const number = parseInt(numberPart, 10);
          if (!isNaN(number) && number > maxNumber) {
            maxNumber = number;
          }
        }
      }

      const nextNumber = maxNumber + 1;

      // New format: SRV-25S000001
      const formattedNumber = String(nextNumber).padStart(6, "0");
      const serviceNumber = `${prefix}-${yearSuffix}S${formattedNumber}`;

      // Double-check that this number doesn't exist (extra safety)
      const existingService = await tx.service.findFirst({
        where: {
          serviceNumber: serviceNumber,
        },
        select: { id: true },
      });

      if (existingService) {
        // If somehow it exists, try the next number
        const nextFormattedNumber = String(nextNumber + 1).padStart(6, "0");
        return `${prefix}-${yearSuffix}S${nextFormattedNumber}`;
      }

      return serviceNumber;
    });

    return {
      success: true,
      nextNumber: result,
    };
  } catch (error) {
    console.error("Error generating next service number:", error);

    // Get current year for fallback
    const fallbackDate = date ? new Date(date) : new Date();
    const yearSuffix = String(fallbackDate.getFullYear()).slice(-2);

    // Fallback
    return {
      success: true,
      nextNumber: `SRV-${yearSuffix}S000001`,
    };
  }
};

/**
 * Add a new service to the database
 */
export const addService = async (
  values: z.infer<typeof EnhancedServiceSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Check subscription limits before creating service
  try {
    const userSubscription = await getUserSubscription(userId);
    const limitCheck = await canCreateTransaction(
      userId,
      userSubscription.plan
    );

    if (!limitCheck.allowed) {
      return {
        error:
          limitCheck.message ||
          "Batas transaksi bulanan tercapai untuk paket Anda.",
      };
    }
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return { error: "Gagal memeriksa batas langganan." };
  }

  // 2. Validate input server-side
  const validatedFields = EnhancedServiceSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    serviceNumber: inputServiceNumber,
    customerName,
    customerPhone,
    customerEmail,
    deviceType,
    deviceBrand,
    deviceModel,
    deviceSerialNumber,
    problemDescription,
    estimatedCost,
    finalCost,
    estimatedCompletionDate,
    diagnosisNotes,
    repairNotes,
    warrantyPeriod,
    customerId,
    customerAddress,
    transactionDate,
    dueDate,
    isDraft,
    attachments,
    // Other fields not used directly in the create operation
    // priorityLevel,
  } = validatedFields.data;

  try {
    // Debug logging
    console.log("Creating service with attachments:", attachments);

    // 2. Generate service number if not provided
    let serviceNumber = inputServiceNumber;
    if (!serviceNumber || serviceNumber.trim() === "") {
      const serviceResult = await getNextServiceNumber(transactionDate);
      if (serviceResult.success) {
        serviceNumber = serviceResult.nextNumber;
      }
    }

    // 3. Create service in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // Create service
      const service = await tx.service.create({
        data: {
          serviceNumber,
          customerName,
          customerPhone,
          customerEmail: customerEmail || null, // Convert empty string to null
          deviceType: deviceType as DeviceType,
          deviceBrand,
          deviceModel,
          deviceSerialNumber: deviceSerialNumber || null,
          problemDescription,
          diagnosisNotes: diagnosisNotes || null,
          repairNotes: repairNotes || null,
          estimatedCost: estimatedCost !== undefined ? estimatedCost : null,
          finalCost: finalCost !== undefined ? finalCost : null,
          warrantyPeriod: warrantyPeriod !== undefined ? warrantyPeriod : 0,
          estimatedCompletionDate: estimatedCompletionDate
            ? new Date(estimatedCompletionDate)
            : null,
          receivedDate: transactionDate
            ? new Date(transactionDate)
            : new Date(),
          status: ServiceStatus.DITERIMA, // Default status for new services
          userId,
          customerId: customerId || null,
          isDraft: isDraft || false,
          lampiran: attachments || [],
        },
      });

      return service;
    });

    // 4. Create initial service history entry
    await db.serviceStatusHistory.create({
      data: {
        status: ServiceStatus.DITERIMA,
        notes: "Servis baru dibuat",
        changedBy: userId,
        serviceId: result.id,
      },
    });

    // 5. Revalidate the services page cache
    revalidatePath("/dashboard/services/management");

    return {
      success: "Servis berhasil ditambahkan!",
      service: {
        id: result.id,
        serviceNumber: result.serviceNumber,
      },
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menambahkan servis ke database." };
  }
};

/**
 * Update an existing service in the database
 */
export const updateService = async (
  id: string,
  values: z.infer<typeof EnhancedServiceSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = EnhancedServiceSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    serviceNumber,
    customerName,
    customerPhone,
    customerEmail,
    deviceType,
    deviceBrand,
    deviceModel,
    deviceSerialNumber,
    problemDescription,
    estimatedCost,
    finalCost,
    estimatedCompletionDate,
    diagnosisNotes,
    repairNotes,
    warrantyPeriod,
    isDraft,
    attachments,
  } = validatedFields.data;

  try {
    // Debug logging
    console.log("Updating service with attachments:", attachments);

    // 2. Update service in database
    const service = await db.service.update({
      where: {
        id,
        userId, // Ensure the service belongs to the current user
      },
      data: {
        serviceNumber,
        customerName,
        customerPhone,
        customerEmail: customerEmail || null, // Convert empty string to null
        deviceType: deviceType as DeviceType,
        deviceBrand,
        deviceModel,
        deviceSerialNumber: deviceSerialNumber || null,
        problemDescription,
        diagnosisNotes: diagnosisNotes || null,
        repairNotes: repairNotes || null,
        estimatedCost: estimatedCost !== undefined ? estimatedCost : null,
        finalCost: finalCost !== undefined ? finalCost : null,
        warrantyPeriod: warrantyPeriod !== undefined ? warrantyPeriod : 0,
        estimatedCompletionDate: estimatedCompletionDate
          ? new Date(estimatedCompletionDate)
          : null,
        isDraft: isDraft !== undefined ? isDraft : false,
        lampiran: attachments || [],
      },
    });

    // 3. Revalidate the services page cache
    revalidatePath("/dashboard/services/management");
    revalidatePath(
      `/dashboard/services/management/detail/${service.serviceNumber}`
    );

    return {
      success: "Servis berhasil diperbarui!",
      service: {
        id: service.id,
        serviceNumber: service.serviceNumber,
      },
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui servis." };
  }
};

/**
 * Update the status of a service
 */
export const updateServiceStatus = async (
  id: string,
  status: ServiceStatus,
  notes?: string
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // 1. Update service status
    const service = await db.service.update({
      where: {
        id,
        userId, // Ensure the service belongs to the current user
      },
      data: {
        status,
        // Update completion date if status is SELESAI_BELUM_DIAMBIL or SELESAI_SUDAH_DIAMBIL
        completedDate:
          status === ServiceStatus.SELESAI_BELUM_DIAMBIL ||
          status === ServiceStatus.SELESAI_SUDAH_DIAMBIL
            ? new Date()
            : undefined,
        // Update delivery date if status is SELESAI_SUDAH_DIAMBIL
        deliveredDate:
          status === ServiceStatus.SELESAI_SUDAH_DIAMBIL
            ? new Date()
            : undefined,
      },
    });

    // 2. Create service history entry
    await db.serviceStatusHistory.create({
      data: {
        status,
        notes: notes || `Status diubah menjadi ${status}`,
        changedBy: userId,
        serviceId: id,
      },
    });

    // 3. Revalidate the services page cache
    revalidatePath("/dashboard/services/management");
    revalidatePath(
      `/dashboard/services/management/detail/${service.serviceNumber}`
    );

    return {
      success: "Status servis berhasil diperbarui!",
      service: {
        id: service.id,
        status: service.status,
      },
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui status servis." };
  }
};

/**
 * Delete a service from the database
 */
export const deleteService = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // 1. First delete all service history entries
    await db.serviceStatusHistory.deleteMany({
      where: {
        serviceId: id,
      },
    });

    // 2. Delete the service
    await db.service.delete({
      where: {
        id,
        userId, // Ensure the service belongs to the current user
      },
    });

    // 3. Revalidate the services page cache
    revalidatePath("/dashboard/services/management");

    return { success: "Servis berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus servis." };
  }
};
