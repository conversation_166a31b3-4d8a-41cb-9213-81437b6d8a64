// Service Management Types

export enum ServiceStatus {
  DITERIMA = "DITERIMA",
  PROSES_MENUNGGU_SPAREPART = "PROSES_MENUNGGU_SPAREPART",
  SELESAI_BELUM_DIAMBIL = "SELESAI_BELUM_DIAMBIL",
  SELESAI_SUDAH_DIAMBIL = "SELESAI_SUDAH_DIAMBIL",
}

export enum DeviceType {
  LAPTOP = "LAPTOP",
  DESKTOP = "DESKTOP",
  PHONE = "PHONE",
  TABLET = "TABLET",
  PRINTER = "PRINTER",
  OTHER = "OTHER",
}

export interface ServiceStatusHistory {
  id: string;
  status: ServiceStatus;
  notes?: string;
  changedAt: string;
  changedBy: string;
  serviceId: string;
}

export interface Service {
  id: string;
  serviceNumber: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  customerAddress?: string;
  deviceType: DeviceType;
  deviceBrand: string;
  deviceModel: string;
  deviceSerialNumber?: string;
  problemDescription: string;
  diagnosisNotes?: string;
  repairNotes?: string;
  estimatedCost?: number;
  finalCost?: number;
  warrantyPeriod?: number;
  priorityLevel?: "LOW" | "MEDIUM" | "HIGH";
  status: ServiceStatus;
  receivedDate: string;
  estimatedCompletionDate?: string;
  completedDate?: string;
  deliveredDate?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  customerId?: string;
  isDraft: boolean;
  lampiran?: { url: string; filename: string }[];
  serviceHistory?: ServiceStatusHistory[];
}

export interface ServiceCounts {
  diterima: number;
  prosesMenungguSparepart: number;
  selesaiBelumDiambil: number;
  selesaiSudahDiambil: number;
  total: number;
  drafts: number;
}

export interface ColumnVisibility {
  serviceNumber: boolean;
  customerName: boolean;
  customerPhone: boolean;
  deviceType: boolean;
  deviceBrand: boolean;
  deviceModel: boolean;
  deviceSerialNumber: boolean;
  status: boolean;
  receivedDate: boolean;
  estimatedCompletionDate: boolean;
  estimatedCost: boolean;
  finalCost: boolean;
  warrantyPeriod: boolean;
}

// Helper function to get display text for service status
export const getServiceStatusDisplayText = (status: ServiceStatus): string => {
  switch (status) {
    case ServiceStatus.DITERIMA:
      return "Diterima";
    case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
      return "Proses/Menunggu Sparepart";
    case ServiceStatus.SELESAI_BELUM_DIAMBIL:
      return "Selesai & Belum Diambil";
    case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
      return "Selesai & Sudah Diambil";
    default:
      return status;
  }
};

// Helper function to get all service status options for dropdowns
export const getServiceStatusOptions = () => [
  { value: ServiceStatus.DITERIMA, label: "Diterima" },
  {
    value: ServiceStatus.PROSES_MENUNGGU_SPAREPART,
    label: "Proses/Menunggu Sparepart",
  },
  {
    value: ServiceStatus.SELESAI_BELUM_DIAMBIL,
    label: "Selesai & Belum Diambil",
  },
  {
    value: ServiceStatus.SELESAI_SUDAH_DIAMBIL,
    label: "Selesai & Sudah Diambil",
  },
];
