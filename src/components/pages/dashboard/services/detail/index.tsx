"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ArrowLeft,
  Pencil,
  Trash,
  User,
  Smartphone,
  FileText,
  Clock,
  Calendar,
  DollarSign,
  Tag,
  History,
  CheckCircle,
  AlertCircle,
  Info,
  Download,
  Eye,
} from "lucide-react";
import { Service, ServiceStatus, DeviceType } from "../types";
import {
  formatDate,
  formatDetailedDate,
  formatRelativeTime,
} from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON>ertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { deleteService } from "@/actions/entities/services";

interface ServiceDetailPageProps {
  service: Service;
}

// Helper function to get file type from filename
const getFileType = (filename: string): string => {
  const extension = filename.split(".").pop()?.toLowerCase();
  switch (extension) {
    case "pdf":
      return "PDF Document";
    case "doc":
    case "docx":
      return "Word Document";
    case "xls":
    case "xlsx":
      return "Excel Spreadsheet";
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
      return "Image";
    case "txt":
      return "Text File";
    default:
      return "Document";
  }
};

// Helper function to get file size (placeholder - would need actual implementation)
const getFileSize = (url: string): string => {
  // This is a placeholder. In a real implementation, you'd fetch the file size
  // from the server or store it when uploading
  return "Unknown size";
};

const ServiceDetailPage: React.FC<ServiceDetailPageProps> = ({ service }) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = React.useState(false);

  // Handle file download
  const handleDownloadFile = (file: { url: string; filename: string }) => {
    const link = document.createElement("a");
    link.href = file.url;
    link.download = file.filename;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle file view
  const handleViewFile = (file: { url: string; filename: string }) => {
    window.open(file.url, "_blank");
  };

  // Function to get device type label
  const getDeviceTypeLabel = (type: DeviceType) => {
    switch (type) {
      case DeviceType.LAPTOP:
        return "Laptop";
      case DeviceType.DESKTOP:
        return "Desktop";
      case DeviceType.PHONE:
        return "Smartphone";
      case DeviceType.TABLET:
        return "Tablet";
      case DeviceType.PRINTER:
        return "Printer";
      case DeviceType.OTHER:
        return "Lainnya";
      default:
        return "Tidak Diketahui";
    }
  };

  // Function to get status badge
  const getStatusBadge = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.DITERIMA:
        return (
          <Badge variant="default" className="bg-blue-500">
            <Clock className="h-3 w-3 mr-1" />
            Diterima
          </Badge>
        );
      case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
        return (
          <Badge variant="default" className="bg-amber-500">
            <Clock className="h-3 w-3 mr-1" />
            Proses/Menunggu Sparepart
          </Badge>
        );
      case ServiceStatus.SELESAI_BELUM_DIAMBIL:
        return (
          <Badge variant="default" className="bg-purple-500">
            <Clock className="h-3 w-3 mr-1" />
            Selesai & Belum Diambil
          </Badge>
        );
      case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
        return (
          <Badge variant="default" className="bg-green-500">
            <Clock className="h-3 w-3 mr-1" />
            Selesai & Sudah Diambil
          </Badge>
        );
      default:
        return (
          <Badge variant="default" className="bg-gray-500">
            <Clock className="h-3 w-3 mr-1" />
            Tidak Diketahui
          </Badge>
        );
    }
  };

  // Handle delete service
  const handleDeleteService = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteService(service.id);
      if (result.success) {
        toast.success(result.success);
        router.push("/dashboard/services/management");
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting service:", error);
      toast.error("Terjadi kesalahan saat menghapus servis.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Detail Servis</h1>
            <p className="text-muted-foreground">
              Informasi lengkap tentang servis {service.serviceNumber}
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild className="gap-2 cursor-pointer">
              <Link
                href="/dashboard/services/management"
                className="cursor-pointer"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Link>
            </Button>
            <Button variant="outline" asChild className="gap-2 cursor-pointer">
              <Link
                href={`/dashboard/services/management/edit/${service.serviceNumber}`}
                className="cursor-pointer"
              >
                <Pencil className="h-4 w-4" />
                Edit
              </Link>
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  className="gap-2 cursor-pointer"
                  disabled={isDeleting}
                >
                  <Trash className="h-4 w-4" />
                  Hapus
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                  <AlertDialogDescription>
                    Apakah Anda yakin ingin menghapus servis ini? Tindakan ini
                    tidak dapat dibatalkan.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Batal</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteService}
                    disabled={isDeleting}
                    className="bg-red-500 hover:bg-red-600"
                  >
                    {isDeleting ? "Menghapus..." : "Hapus"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="mb-4 cursor-pointer">
            <TabsTrigger value="details" className="cursor-pointer">
              Detail Servis
            </TabsTrigger>
            <TabsTrigger value="history" className="cursor-pointer">
              Riwayat Status
            </TabsTrigger>
            <TabsTrigger value="attachments" className="cursor-pointer">
              Lampiran
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Service Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Tag className="h-5 w-5 mr-2" />
                    Informasi Servis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="font-medium">Nomor Servis</div>
                      <div>{service.serviceNumber}</div>

                      <div className="font-medium">Status</div>
                      <div>{getStatusBadge(service.status)}</div>

                      <div className="font-medium">Tanggal Masuk</div>
                      <div>{formatDate(service.receivedDate)}</div>

                      {service.estimatedCompletionDate && (
                        <>
                          <div className="font-medium">Estimasi Selesai</div>
                          <div>
                            {formatDate(service.estimatedCompletionDate)}
                          </div>
                        </>
                      )}

                      {service.completedDate && (
                        <>
                          <div className="font-medium">Tanggal Selesai</div>
                          <div>{formatDate(service.completedDate)}</div>
                        </>
                      )}

                      {service.deliveredDate && (
                        <>
                          <div className="font-medium">Tanggal Diambil</div>
                          <div>{formatDate(service.deliveredDate)}</div>
                        </>
                      )}

                      {service.estimatedCost !== undefined && (
                        <>
                          <div className="font-medium">Estimasi Biaya</div>
                          <div>
                            Rp {service.estimatedCost.toLocaleString("id-ID")}
                          </div>
                        </>
                      )}

                      {service.finalCost !== undefined &&
                        service.finalCost > 0 && (
                          <>
                            <div className="font-medium">Biaya Final</div>
                            <div>
                              Rp {service.finalCost.toLocaleString("id-ID")}
                            </div>
                          </>
                        )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Customer Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Informasi Pelanggan
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="font-medium">Nama</div>
                      <div>{service.customerName}</div>

                      <div className="font-medium">Telepon</div>
                      <div>{service.customerPhone}</div>

                      {service.customerEmail && (
                        <>
                          <div className="font-medium">Email</div>
                          <div>{service.customerEmail}</div>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Device Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Smartphone className="h-5 w-5 mr-2" />
                    Informasi Perangkat
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="font-medium">Tipe</div>
                      <div>{getDeviceTypeLabel(service.deviceType)}</div>

                      <div className="font-medium">Merek</div>
                      <div>{service.deviceBrand}</div>

                      <div className="font-medium">Model</div>
                      <div>{service.deviceModel}</div>

                      {service.deviceSerialNumber && (
                        <>
                          <div className="font-medium">Nomor Seri</div>
                          <div>{service.deviceSerialNumber}</div>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Problem Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Deskripsi Masalah
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="whitespace-pre-wrap">
                      {service.problemDescription}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Diagnosis Notes */}
              {service.diagnosisNotes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      Catatan Diagnosis
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="whitespace-pre-wrap">
                        {service.diagnosisNotes}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Repair Notes */}
              {service.repairNotes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      Catatan Perbaikan
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="whitespace-pre-wrap">
                        {service.repairNotes}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-blue-600" />
                  Riwayat Status Servis
                </CardTitle>
                <CardDescription>
                  Perubahan status servis dari waktu ke waktu dengan detail
                  lengkap
                </CardDescription>
              </CardHeader>
              <CardContent>
                {service.serviceHistory && service.serviceHistory.length > 0 ? (
                  <div className="space-y-6">
                    {service.serviceHistory
                      .sort(
                        (a, b) =>
                          new Date(b.changedAt).getTime() -
                          new Date(a.changedAt).getTime()
                      )
                      .map((history, index) => (
                        <div
                          key={history.id}
                          className="relative pl-8 pb-6 last:pb-0"
                        >
                          {/* Timeline line */}
                          {index < service.serviceHistory!.length - 1 && (
                            <div className="absolute left-3 top-8 w-0.5 h-full bg-gray-200 dark:bg-gray-700"></div>
                          )}

                          {/* Timeline dot */}
                          <div className="absolute left-0 top-2 w-6 h-6 rounded-full border-2 bg-white dark:bg-gray-900 flex items-center justify-center">
                            {history.status === "DITERIMA" && (
                              <Info className="h-3 w-3 text-blue-600" />
                            )}
                            {history.status === "PROSES_MENUNGGU_SPAREPART" && (
                              <Clock className="h-3 w-3 text-yellow-600" />
                            )}
                            {(history.status === "SELESAI_BELUM_DIAMBIL" ||
                              history.status === "SELESAI_SUDAH_DIAMBIL") && (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            )}
                          </div>

                          {/* Content */}
                          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  {getStatusBadge(history.status)}
                                  <span className="text-xs text-muted-foreground">
                                    {formatRelativeTime(history.changedAt)}
                                  </span>
                                </div>

                                {history.notes && (
                                  <div className="bg-white dark:bg-gray-900 rounded-md p-3 border border-gray-200 dark:border-gray-600">
                                    <p className="text-sm text-gray-700 dark:text-gray-300">
                                      <span className="font-medium text-gray-900 dark:text-gray-100">
                                        Catatan:
                                      </span>{" "}
                                      {history.notes}
                                    </p>
                                  </div>
                                )}
                              </div>

                              <div className="text-right">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {formatDetailedDate(history.changedAt)}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                  Diubah oleh: {history.changedBy}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <History className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Belum Ada Riwayat Status
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Riwayat perubahan status akan muncul di sini setelah ada
                      aktivitas pada servis ini.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="attachments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Lampiran Dokumen
                </CardTitle>
                <CardDescription>
                  Dokumen dan file yang dilampirkan pada servis ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                {service.lampiran && service.lampiran.length > 0 ? (
                  <div className="space-y-4">
                    {service.lampiran.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                      >
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <FileText className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100">
                              {file.filename}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {getFileType(file.filename)} •{" "}
                              {getFileSize(file.url)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadFile(file)}
                            className="gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewFile(file)}
                            className="gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            Lihat
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Tidak Ada Lampiran
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Belum ada dokumen atau file yang dilampirkan pada servis
                      ini.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ServiceDetailPage;
