import React from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Lock } from "lucide-react";
import type { ColumnVisibility } from "../types";
import { customersColumnConfig } from "../config/columnConfig";
import { CustomerImportExport } from "./CustomerImportExport";
import { CustomerFilter, CustomerFilterState } from "./CustomerFilter";
import { useContactLimits } from "@/hooks/useSubscriptionLimits";
import { toast } from "sonner";

interface CustomerActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filters: CustomerFilterState;
  onFilterChange: (filters: CustomerFilterState) => void;
  onRefresh?: () => void; // Add refresh callback for after import
}

export const CustomerActions: React.FC<CustomerActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  filters,
  onFilterChange,
  onRefresh,
}) => {
  const {
    canCreateContact,
    contactMessage,
    currentContactUsage,
    contactLimit,
    isLoading: limitsLoading,
  } = useContactLimits();

  const handleAddCustomerClick = (e: React.MouseEvent) => {
    if (!canCreateContact) {
      e.preventDefault();
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      return;
    }
  };

  return (
    <div className="flex flex-wrap items-center justify-between gap-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <AdjustmentsHorizontalIcon className="mr-2 h-4 w-4" />
              Kolom
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Pilih Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
              {customersColumnConfig.map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.key}
                  checked={columnVisibility[column.key]}
                  onCheckedChange={(checked) =>
                    setColumnVisibility((prev) => ({
                      ...prev,
                      [column.key]: !!checked,
                    }))
                  }
                  onSelect={(e) => e.preventDefault()}
                >
                  {column.label}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Component */}
        <CustomerFilter filters={filters} onFilterChange={onFilterChange} />

        {/* Import/Export Component */}
        <CustomerImportExport onRefresh={onRefresh} />
      </div>

      <div className="flex items-center gap-2 w-full sm:w-auto">
        {/* Search Input */}
        <div className="relative flex-grow">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400 dark:text-gray-500"
              aria-hidden="true"
            />
          </div>
          <input
            type="text"
            placeholder="Cari Pelanggan"
            className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-10 pr-3 leading-5 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-indigo-500 dark:focus:border-indigo-400 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:focus:ring-indigo-400 sm:text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Add Customer Button */}
        {canCreateContact ? (
          <Link
            href="/dashboard/customers/new"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
          >
            <PlusIcon className="mr-2 h-5 w-5" />
            Tambah
          </Link>
        ) : (
          <Button
            disabled
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm cursor-not-allowed"
            onClick={handleAddCustomerClick}
            title={contactMessage || "Batas kontak tercapai"}
          >
            <Lock className="mr-2 h-5 w-5" />
            Tambah
          </Button>
        )}
      </div>
    </div>
  );
};
