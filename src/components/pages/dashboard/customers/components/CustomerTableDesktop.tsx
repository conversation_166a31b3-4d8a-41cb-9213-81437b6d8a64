import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Customer, ColumnVisibility } from "../types";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  CreditCard,
  UserCheck,
  Trash,
  LoaderCircle,
  Edit,
  Eye,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { deleteCustomer } from "@/actions/entities/customers";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CustomerTableDesktopProps {
  customers: Customer[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
}

export const CustomerTableDesktop: React.FC<CustomerTableDesktopProps> = ({
  customers,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
}) => {
  const router = useRouter();
  const [deletingCustomerId, setDeletingCustomerId] = useState<string | null>(
    null
  );

  // Handle delete customer
  const handleDeleteCustomer = async (id: string) => {
    console.log(
      `[CustomerTable] Starting delete process for customer ID: ${id}`
    );
    setDeletingCustomerId(id);
    try {
      console.log(
        `[CustomerTable] Calling deleteCustomer action for ID: ${id}`
      );
      const result = await deleteCustomer(id);
      console.log(`[CustomerTable] Delete result:`, result);

      if (result.success) {
        console.log(`[CustomerTable] Delete successful, showing success toast`);
        toast.success(result.success);
        console.log(`[CustomerTable] Calling router.refresh()`);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        console.log(`[CustomerTable] Delete failed with error:`, result.error);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("[CustomerTable] Exception during delete:", error);
      toast.error("Terjadi kesalahan saat menghapus pelanggan.");
    } finally {
      console.log(`[CustomerTable] Cleaning up delete state for ID: ${id}`);
      setDeletingCustomerId(null);
    }
  };
  return (
    <div className="space-y-4">
      {/* Debug Test Button */}
      <div className="p-4 bg-yellow-100 border border-yellow-300 rounded">
        <p className="text-sm text-yellow-800 mb-2">
          Debug Test: Click to test delete function directly
        </p>
        <Button
          onClick={() => {
            console.log("[CustomerTable] Test button clicked");
            if (customers.length > 0) {
              handleDeleteCustomer(customers[0].id);
            }
          }}
          className="bg-yellow-500 hover:bg-yellow-600 text-white"
        >
          Test Delete First Customer
        </Button>
      </div>

      <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
        <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
          <thead className="text-xs text-gray-700 dark:text-gray-400 uppercase bg-gray-50 dark:bg-gray-700">
            <tr>
              {columnVisibility.name && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    Nama Pelanggan
                    {getSortIcon("name")}
                  </div>
                </th>
              )}
              {columnVisibility.contactName && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("contactName")}
                >
                  <div className="flex items-center">
                    Nama Kontak
                    {getSortIcon("contactName")}
                  </div>
                </th>
              )}
              {columnVisibility.email && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("email")}
                >
                  <div className="flex items-center">
                    Email
                    {getSortIcon("email")}
                  </div>
                </th>
              )}
              {columnVisibility.phone && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("phone")}
                >
                  <div className="flex items-center">
                    Telepon
                    {getSortIcon("phone")}
                  </div>
                </th>
              )}
              {columnVisibility.address && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("address")}
                >
                  <div className="flex items-center">
                    Alamat
                    {getSortIcon("address")}
                  </div>
                </th>
              )}
              {columnVisibility.NIK && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("NIK")}
                >
                  <div className="flex items-center">
                    NIK
                    {getSortIcon("NIK")}
                  </div>
                </th>
              )}
              {columnVisibility.NPWP && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("NPWP")}
                >
                  <div className="flex items-center">
                    NPWP
                    {getSortIcon("NPWP")}
                  </div>
                </th>
              )}
              {columnVisibility.createdAt && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("createdAt")}
                >
                  <div className="flex items-center">
                    Tanggal Dibuat
                    {getSortIcon("createdAt")}
                  </div>
                </th>
              )}
              {columnVisibility.updatedAt && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("updatedAt")}
                >
                  <div className="flex items-center">
                    Tanggal Diperbarui
                    {getSortIcon("updatedAt")}
                  </div>
                </th>
              )}
              {columnVisibility.notes && (
                <th
                  scope="col"
                  className="px-6 py-3 cursor-pointer"
                  onClick={() => handleSort("notes")}
                >
                  <div className="flex items-center">
                    Catatan
                    {getSortIcon("notes")}
                  </div>
                </th>
              )}
              <th scope="col" className="px-6 py-3 text-right">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody>
            {customers.length > 0 ? (
              customers.map((customer) => (
                <tr
                  key={customer.id}
                  className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  {columnVisibility.name && (
                    <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div className="flex items-center">
                        <Badge className="mr-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200">
                          <User className="h-3 w-3 mr-1" />
                        </Badge>
                        {customer.name}
                      </div>
                    </td>
                  )}
                  {columnVisibility.contactName && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {customer.contactName || "-"}
                      </div>
                    </td>
                  )}
                  {columnVisibility.email && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {customer.email ? (
                          <>
                            <Mail className="h-4 w-4 mr-1 text-gray-400" />
                            {customer.email}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  {columnVisibility.phone && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {customer.phone ? (
                          <>
                            <Phone className="h-4 w-4 mr-1 text-gray-400" />
                            {customer.phone}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  {columnVisibility.address && (
                    <td className="px-6 py-4">
                      <div className="flex items-start">
                        {customer.address ? (
                          <>
                            <MapPin className="h-4 w-4 mr-1 text-gray-400 mt-0.5" />
                            <span className="line-clamp-2">
                              {customer.address}
                            </span>
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  {columnVisibility.NIK && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {customer.NIK ? (
                          <>
                            <UserCheck className="h-4 w-4 mr-1 text-gray-400" />
                            {customer.NIK}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  {columnVisibility.NPWP && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {customer.NPWP ? (
                          <>
                            <CreditCard className="h-4 w-4 mr-1 text-gray-400" />
                            {customer.NPWP}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  {columnVisibility.createdAt && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                        {format(customer.createdAt, "dd MMM yyyy", {
                          locale: id,
                        })}
                      </div>
                    </td>
                  )}
                  {columnVisibility.updatedAt && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                        {format(customer.updatedAt, "dd MMM yyyy", {
                          locale: id,
                        })}
                      </div>
                    </td>
                  )}
                  {columnVisibility.notes && (
                    <td className="px-6 py-4">
                      <div className="flex items-start">
                        {customer.notes ? (
                          <>
                            <FileText className="h-4 w-4 mr-1 text-gray-400 mt-0.5" />
                            <span className="line-clamp-2">
                              {customer.notes}
                            </span>
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </td>
                  )}
                  <td className="px-6 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                    <TooltipProvider>
                      <div className="flex justify-end space-x-1">
                        {/* Detail Button */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                              asChild
                            >
                              <Link
                                href={`/dashboard/customers/detail/${customer.id}`}
                              >
                                <Eye className="h-4 w-4" />
                                <span className="sr-only">Detail</span>
                              </Link>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Detail</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Edit Button */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 bg-yellow-500 text-white cursor-pointer hover:bg-yellow-400"
                              asChild
                            >
                              <Link
                                href={`/dashboard/customers/edit/${customer.id}`}
                              >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Link>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Edit</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Delete Button */}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                              disabled={deletingCustomerId === customer.id}
                              data-deleting={deletingCustomerId === customer.id}
                              onClick={() => {
                                console.log(
                                  `[CustomerTable] Delete button clicked for customer ID: ${customer.id}`
                                );
                              }}
                            >
                              {deletingCustomerId === customer.id ? (
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash className="h-4 w-4" />
                              )}
                              <span className="sr-only">Delete</span>
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Konfirmasi Hapus
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Apakah Anda yakin ingin menghapus pelanggan{" "}
                                {customer.name}? Tindakan ini tidak dapat
                                dibatalkan.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="cursor-pointer">
                                Batal
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => {
                                  console.log(
                                    `[CustomerTable] AlertDialogAction clicked for customer ID: ${customer.id}`
                                  );
                                  handleDeleteCustomer(customer.id);
                                }}
                                disabled={deletingCustomerId === customer.id}
                                className="cursor-pointer bg-red-500 hover:bg-red-600"
                              >
                                {deletingCustomerId === customer.id ? (
                                  <div className="flex items-center gap-2">
                                    <LoaderCircle className="h-4 w-4 animate-spin" />
                                    Menghapus...
                                  </div>
                                ) : (
                                  "Hapus"
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TooltipProvider>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td
                  colSpan={
                    Object.values(columnVisibility).filter(Boolean).length + 1
                  }
                  className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                >
                  {searchTerm
                    ? "Tidak ada pelanggan yang sesuai dengan pencarian."
                    : "Belum ada data pelanggan. Tambahkan pelanggan baru."}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
