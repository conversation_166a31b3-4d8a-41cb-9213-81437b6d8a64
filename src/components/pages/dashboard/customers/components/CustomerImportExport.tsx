"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { getCustomerReportData } from "@/actions/reports/reports";

interface ImportSummary {
  customersCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  format: "excel" | "csv";
  includeSummary: boolean;
  includeCharts: boolean;
}

interface CustomerImportExportProps {
  onRefresh?: () => void; // Callback to refresh the customers list after import
}

// Function to create customer-only Excel report
const createCustomerOnlyExcelReport = (
  customers: any[],
  options: {
    reportTitle: string;
    includeSummary: boolean;
    totalCustomers: number;
  }
) => {
  const workbook = XLSX.utils.book_new();

  // Create header info
  const headerData = [
    [options.reportTitle],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [`Total Pelanggan: ${options.totalCustomers}`],
    [], // Empty row
  ];

  // Define column headers
  const columnHeaders = [
    "ID Pelanggan",
    "Nama Pelanggan",
    "Nama Depan",
    "Nama Tengah",
    "Nama Belakang",
    "Nama Kontak",
    "Telepon",
    "Email",
    "Jenis Identitas",
    "Nomor Identitas",
    "NIK",
    "NPWP",
    "Nama Perusahaan",
    "Alamat",
    "Alamat Penagihan",
    "Alamat Pengiriman",
    "Catatan",
  ];

  // Prepare customer data
  const customerData = customers.map((customer) => {
    return [
      customer.id || "-",
      customer.name || "-",
      customer.firstName || "-",
      customer.middleName || "-",
      customer.lastName || "-",
      customer.contactName || "-",
      customer.phone || "-",
      customer.email || "-",
      customer.identityType || "-",
      customer.identityNumber || "-",
      customer.NIK || "-",
      customer.NPWP || "-",
      customer.companyName || "-",
      customer.address || "-",
      customer.billingAddress || "-",
      customer.shippingAddress || "-",
      customer.notes || "-",
    ];
  });

  // Combine all data
  const worksheetData = [...headerData, columnHeaders, ...customerData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 25 }, // ID Pelanggan
    { wch: 30 }, // Nama Pelanggan
    { wch: 20 }, // Nama Depan
    { wch: 20 }, // Nama Tengah
    { wch: 20 }, // Nama Belakang
    { wch: 25 }, // Nama Kontak
    { wch: 15 }, // Telepon
    { wch: 30 }, // Email
    { wch: 15 }, // Jenis Identitas
    { wch: 20 }, // Nomor Identitas
    { wch: 20 }, // NIK
    { wch: 20 }, // NPWP
    { wch: 30 }, // Nama Perusahaan
    { wch: 40 }, // Alamat
    { wch: 40 }, // Alamat Penagihan
    { wch: 40 }, // Alamat Pengiriman
    { wch: 50 }, // Catatan
  ];
  worksheet["!cols"] = columnWidths;

  // Style the header rows
  const headerStyle = {
    font: { bold: true, sz: 14 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "E3F2FD" } },
  };

  const columnHeaderStyle = {
    font: { bold: true, sz: 12 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "BBDEFB" } },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  };

  // Apply styles to header
  if (worksheet["A1"]) worksheet["A1"].s = headerStyle;
  if (worksheet["A2"]) worksheet["A2"].s = { font: { sz: 10 } };
  if (worksheet["A3"]) worksheet["A3"].s = { font: { sz: 10 } };

  // Apply styles to column headers (row 5)
  const headerRowIndex = 5;
  columnHeaders.forEach((_, colIndex) => {
    const cellAddress = XLSX.utils.encode_cell({
      r: headerRowIndex - 1,
      c: colIndex,
    });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = columnHeaderStyle;
    }
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Pelanggan");

  return workbook;
};

export const CustomerImportExport: React.FC<CustomerImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    format: "excel",
    includeSummary: true,
    includeCharts: false,
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      // Create a simple template for customers
      const workbook = XLSX.utils.book_new();

      // Template headers
      const headers = [
        "Nama Pelanggan*",
        "Nama Depan",
        "Nama Tengah",
        "Nama Belakang",
        "Nama Kontak",
        "Telepon",
        "Email",
        "Jenis Identitas",
        "Nomor Identitas",
        "NIK",
        "NPWP",
        "Nama Perusahaan",
        "Alamat",
        "Alamat Penagihan",
        "Alamat Pengiriman",
        "Catatan",
      ];

      // Create worksheet with headers
      const worksheet = XLSX.utils.aoa_to_sheet([headers]);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Template Pelanggan");

      const fileName = `template-import-pelanggan-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Advanced export handler
  const handleAdvancedExport = async () => {
    try {
      setIsExporting(true);
      setExportProgress(0);

      // Determine period label
      let periodLabel: string = "";
      if (exportConfig.reportType === "harian") {
        periodLabel = `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        const monthNames = [
          "Januari",
          "Februari",
          "Maret",
          "April",
          "Mei",
          "Juni",
          "Juli",
          "Agustus",
          "September",
          "Oktober",
          "November",
          "Desember",
        ];
        periodLabel = `Bulanan - ${monthNames[exportConfig.selectedMonth]} ${exportConfig.selectedYear}`;
      } else if (exportConfig.reportType === "tahunan") {
        periodLabel = `Tahunan - ${exportConfig.selectedYear}`;
      }

      setExportProgress(40);

      // Fetch customer data
      const customerResult = await getCustomerReportData();

      setExportProgress(70);

      if (customerResult.error) {
        console.error("Customer data fetch error:", customerResult.error);
        throw new Error(customerResult.error);
      }

      // Check if we have data
      if (!customerResult.data || customerResult.data.length === 0) {
        toast.error(
          "Tidak ada data pelanggan untuk diekspor pada periode yang dipilih."
        );
        return;
      }

      console.log(
        "Customer data fetched:",
        customerResult.data?.length || 0,
        "customers"
      );

      const reportData = {
        customers: customerResult.data,
        summary: {
          totalCustomers: customerResult.data.length,
          period: periodLabel,
          generatedAt: new Date(),
        },
      };

      setExportProgress(85);

      if (exportConfig.format === "excel") {
        // Generate Excel export - Customers only
        const workbook = createCustomerOnlyExcelReport(reportData.customers, {
          reportTitle: `Data Pelanggan - ${periodLabel}`,
          includeSummary: exportConfig.includeSummary,
          totalCustomers: reportData.customers.length,
        });

        setExportProgress(100);

        const fileName = `data-pelanggan-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      } else {
        // Generate CSV export - Customers only
        let csvContent = `Data Pelanggan - ${periodLabel}\n`;
        csvContent += `Diekspor pada: ${new Date().toLocaleString("id-ID")}\n`;
        csvContent += `Total Pelanggan: ${reportData.customers.length}\n\n`;

        // CSV Headers
        csvContent +=
          "ID Pelanggan,Nama Pelanggan,Nama Kontak,Email,Telepon,Alamat,NIK,NPWP,Catatan,Tanggal Dibuat,Terakhir Diperbarui\n";

        // CSV Data
        reportData.customers.forEach((customer) => {
          const row = [
            customer.id || "-",
            customer.name || "-",
            customer.contactName || "-",
            customer.email || "-",
            customer.phone || "-",
            (customer.address || "-").replace(/,/g, ";"), // Replace commas to avoid CSV issues
            customer.NIK || "-",
            customer.NPWP || "-",
            (customer.notes || "-").replace(/,/g, ";"), // Replace commas to avoid CSV issues
            new Date(customer.createdAt).toLocaleDateString("id-ID"),
            new Date(customer.updatedAt).toLocaleDateString("id-ID"),
          ];
          csvContent += row.map((field) => `"${field}"`).join(",") + "\n";
        });

        // Download CSV
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `data-pelanggan-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.csv`
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success("Data pelanggan berhasil diekspor!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data pelanggan");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      // Read file for future implementation
      await file.arrayBuffer();
      setImportProgress(40);

      // TODO: Implement customer import API call
      // const result = await importCustomers(arrayBuffer);
      setImportProgress(80);

      // Simulate success for now
      setImportProgress(100);
      setImportSummary({
        customersCreated: 0,
        errors: ["Import functionality will be implemented soon"],
      });
      toast.info("Import functionality will be implemented soon");

      // Auto-refresh data on successful import
      if (onRefresh) {
        setTimeout(() => {
          onRefresh();
        }, 1500);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Pelanggan
            </DialogTitle>
            <DialogDescription>
              Import data pelanggan dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Pelanggan:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data pelanggan sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data pelanggan...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Hasil Import:
                </h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Pelanggan berhasil dibuat:{" "}
                    {importSummary.customersCreated}
                  </p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        Error:
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-md max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Data Pelanggan
            </DialogTitle>
            <DialogDescription>
              Pilih periode dan format untuk mengekspor data pelanggan
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 py-4">
            {/* Report Type Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Jenis Laporan</Label>
              <div className="grid grid-cols-1 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-2">
                        <type.icon className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {type.label}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {[
                        "Januari",
                        "Februari",
                        "Maret",
                        "April",
                        "Mei",
                        "Juni",
                        "Juli",
                        "Agustus",
                        "September",
                        "Oktober",
                        "November",
                        "Desember",
                      ].map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-9"
                  />
                </div>
              )}
            </div>

            {/* Format Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <Select
                value={exportConfig.format}
                onValueChange={(value) =>
                  setExportConfig((prev) => ({
                    ...prev,
                    format: value as any,
                  }))
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label htmlFor="includeSummary" className="text-sm">
                    Sertakan ringkasan data
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={handleAdvancedExport} disabled={isExporting}>
              {isExporting ? "Mengekspor..." : "Export"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Data Pelanggan
              </DialogTitle>
              <DialogDescription>
                Mohon tunggu, sedang memproses data pelanggan...
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="w-full" />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
