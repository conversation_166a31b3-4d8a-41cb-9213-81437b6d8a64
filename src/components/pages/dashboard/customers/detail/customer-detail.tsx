"use client";

import React from "react";
import Link from "next/link";
import { Customer as PrismaCustomer } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Edit,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  CreditCard,
  UserCheck,
  Building2,
  PhoneCall,
  IdCard,
  Banknote,
  Home,
  Truck,
} from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface CustomerDetailPageProps {
  customer: PrismaCustomer;
}

const CustomerDetailPage: React.FC<CustomerDetailPageProps> = ({
  customer,
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts?tab=pelanggan">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                Detail Pelanggan
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Informasi lengkap pelanggan
              </p>
            </div>
          </div>
          <Link href={`/dashboard/customers/edit/${customer.id}`}>
            <Button className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>

      {/* Customer Information */}
      <div className="max-w-4xl mx-auto px-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Customer Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {customer.name}
                </h2>
                {customer.contactName && (
                  <p className="text-gray-600 dark:text-gray-400">
                    Kontak: {customer.contactName}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Personal
                </h3>

                <div className="space-y-3">
                  {customer.firstName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Depan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.firstName}
                      </span>
                    </div>
                  )}

                  {customer.middleName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Tengah:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.middleName}
                      </span>
                    </div>
                  )}

                  {customer.lastName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Belakang:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.lastName}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Nama Kontak:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.contactName || "-"}
                    </span>
                  </div>

                  {customer.companyName && (
                    <div className="flex items-center gap-3">
                      <Building2 className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Perusahaan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.companyName}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Kontak
                </h3>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Email:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.email || "-"}
                    </span>
                  </div>

                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      HP:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.phone || "-"}
                    </span>
                  </div>

                  {customer.telephone && (
                    <div className="flex items-center gap-3">
                      <PhoneCall className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Telepon:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.telephone}
                      </span>
                    </div>
                  )}

                  {customer.fax && (
                    <div className="flex items-center gap-3">
                      <PhoneCall className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Fax:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.fax}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Identity Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Identitas
                </h3>

                <div className="space-y-3">
                  {customer.identityType && (
                    <div className="flex items-center gap-3">
                      <IdCard className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Jenis ID:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.identityType}
                      </span>
                    </div>
                  )}

                  {customer.identityNumber && (
                    <div className="flex items-center gap-3">
                      <IdCard className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nomor ID:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.identityNumber}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center gap-3">
                    <UserCheck className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      NIK:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.NIK || "-"}
                    </span>
                  </div>

                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      NPWP:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.NPWP || "-"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4 lg:col-span-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Alamat
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    {customer.billingAddress && (
                      <div className="flex items-start gap-3">
                        <Home className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Tagihan:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {customer.billingAddress}
                        </span>
                      </div>
                    )}

                    {customer.shippingAddress && (
                      <div className="flex items-start gap-3">
                        <Truck className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Kirim:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {customer.shippingAddress}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Bank Information */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Bank
                </h3>

                {customer.bankName && (
                  <div className="flex items-center gap-3">
                    <Banknote className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Bank:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.bankName}
                    </span>
                  </div>
                )}

                {customer.bankBranch && (
                  <div className="flex items-center gap-3">
                    <Banknote className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Cabang:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.bankBranch}
                    </span>
                  </div>
                )}

                {customer.accountHolder && (
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Pemegang:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.accountHolder}
                    </span>
                  </div>
                )}

                {customer.accountNumber && (
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      No. Rekening:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white font-mono">
                      {customer.accountNumber}
                    </span>
                  </div>
                )}
              </div>

              {/* Additional Information */}
              <div className="space-y-4 lg:col-span-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Tambahan
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    {customer.otherInfo && (
                      <div className="flex items-start gap-3">
                        <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Info Lain:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {customer.otherInfo}
                        </span>
                      </div>
                    )}

                    <div className="flex items-start gap-3">
                      <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Catatan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {customer.notes || "-"}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Dibuat:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {format(
                          new Date(customer.createdAt),
                          "dd MMMM yyyy 'pukul' HH:mm",
                          { locale: id }
                        )}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Diperbarui:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {format(
                          new Date(customer.updatedAt),
                          "dd MMMM yyyy 'pukul' HH:mm",
                          { locale: id }
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        ID Pelanggan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white font-mono">
                        {customer.id}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Status:
                      </span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 hover:bg-primary/10">
                        Aktif
                      </Badge>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Tipe Kontak:
                      </span>
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 hover:bg-primary/10">
                        Pelanggan
                      </Badge>
                    </div>

                    {customer.sameAsShipping && (
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Sama:
                        </span>
                        <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                          Ya
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDetailPage;
