import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Product, ColumnVisibility } from "../types"; // Import from the new types file
import { Trash, LoaderCircle, Printer, Edit, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { deleteProduct } from "@/actions/entities/products";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { productsColumnConfig } from "../config/columnConfig";

interface ProductTableDesktopProps {
  products: Product[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  getStockStatusBadge: (stock: number) => React.ReactNode;
  searchTerm: string;
}

export const ProductTableDesktop: React.FC<ProductTableDesktopProps> = ({
  products,
  columnVisibility,
  handleSort,
  getSortIcon,
  getStockStatusBadge,
  searchTerm,
}) => {
  const router = useRouter();
  const [deletingProductId, setDeletingProductId] = useState<string | null>(
    null
  );

  // Helper function to render cell content based on column key
  const renderCellContent = (
    product: Product,
    columnKey: keyof ColumnVisibility
  ) => {
    switch (columnKey) {
      case "image":
        return product.image ? (
          <div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 dark:bg-gray-700">
            <img
              src={product.image}
              alt={product.name}
              className="h-full w-full object-cover"
            />
          </div>
        ) : (
          <div className="h-10 w-10 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
            <span className="text-xs text-gray-400">No img</span>
          </div>
        );
      case "name":
        return (
          <Link
            href={`/dashboard/products/detail/${product.id}`}
            className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
          >
            {product.name}
          </Link>
        );
      case "sku":
        return product.sku || "-";
      case "barcode":
        return product.barcode || "-";
      case "unit":
        return product.unit || "Pcs";
      case "stock":
        return <span className="font-medium">{product.stock}</span>;
      case "cost":
        return product.cost
          ? `Rp ${product.cost.toLocaleString("id-ID")}`
          : "-";
      case "sellPrice":
        return `Rp ${product.price.toLocaleString("id-ID")}`;
      case "wholesalePrice":
        return product.wholesalePrice ? (
          <span className="text-blue-600 dark:text-blue-400">
            Rp {product.wholesalePrice.toLocaleString("id-ID")}
          </span>
        ) : (
          "-"
        );
      case "category":
        return product.category?.name || "-";
      case "tags":
        return (
          <div className="flex flex-wrap gap-1">
            {product.tags && product.tags.length > 0 ? (
              product.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                >
                  {typeof tag === "string" ? tag : tag.name}
                </span>
              ))
            ) : (
              <span key="no-tags">-</span>
            )}
          </div>
        );
      case "colorVariants":
        return (
          <div className="flex flex-wrap gap-1">
            {product.hasVariants &&
            product.variants &&
            product.variants.length > 0 ? (
              product.variants.map((variant, index) => (
                <div key={index} className="flex items-center gap-1">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: variant.colorCode }}
                  ></div>
                  <span className="text-xs">{variant.colorName}</span>
                </div>
              ))
            ) : (
              <span>-</span>
            )}
          </div>
        );
      case "stockStatus":
        return getStockStatusBadge(product.stock);
      case "price":
        return `Rp ${product.price.toLocaleString("id-ID")}`;
      default:
        return "-";
    }
  };

  // Handle delete product
  const handleDeleteProduct = async (id: string) => {
    setDeletingProductId(id);
    try {
      const result = await deleteProduct(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Terjadi kesalahan saat menghapus produk.");
    } finally {
      setDeletingProductId(null);
    }
  };

  // Handle print product (products don't have invoices, so show info message)
  const handlePrintProduct = (product: Product) => {
    toast.info(
      "Fitur cetak untuk produk akan segera hadir! Saat ini hanya tersedia untuk transaksi penjualan dan pembelian."
    );
  };
  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            {productsColumnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() =>
                      column.key !== "image" &&
                      column.key !== "colorVariants" &&
                      column.key !== "stockStatus"
                        ? handleSort(column.sortKey)
                        : undefined
                    }
                  >
                    <div className="flex items-center">
                      {column.key === "image" ? "Gambar" : column.label}
                      {column.key !== "image" &&
                        column.key !== "colorVariants" &&
                        column.key !== "stockStatus" &&
                        getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {products.length > 0 ? (
            products.map((product) => (
              <tr
                key={product.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {productsColumnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "name"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(product, column.key)}
                      </td>
                    )
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end space-x-1">
                    {/* Share Button (WhatsApp) */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                          onClick={() => {
                            const message = `Produk ${product.name} - Harga: ${new Intl.NumberFormat(
                              "id-ID",
                              {
                                style: "currency",
                                currency: "IDR",
                                minimumFractionDigits: 0,
                              }
                            ).format(product.price)} - Stok: ${product.stock}`;
                            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                            window.open(whatsappUrl, "_blank");
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                          <span className="sr-only">Share via WhatsApp</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bagikan</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Edit Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                          onClick={() =>
                            router.push(
                              `/dashboard/products/edit/${product.id}`
                            )
                          }
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                              disabled={deletingProductId === product.id}
                            >
                              {deletingProductId === product.id ? (
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash className="h-4 w-4" />
                              )}
                              <span className="sr-only">Delete</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Hapus</p>
                          </TooltipContent>
                        </Tooltip>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus produk{" "}
                            {product.name}? Tindakan ini tidak dapat dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Batal
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteProduct(product.id)}
                            disabled={deletingProductId === product.id}
                            className="cursor-pointer bg-red-500 hover:bg-red-600"
                          >
                            {deletingProductId === product.id ? (
                              <div className="flex items-center gap-2">
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                                <span>Menghapus...</span>
                              </div>
                            ) : (
                              "Hapus"
                            )}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 1
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada produk yang sesuai dengan pencarian."
                  : "Belum ada data produk."}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
