import React from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Lock } from "lucide-react";
import type { ColumnVisibility } from "../types";
import { productsColumnConfig } from "../config/columnConfig";
import { ProductImportExport } from "./ProductImportExport";
import { ProductFilter, ProductFilterState } from "./ProductFilter";
import { useProductLimits } from "@/hooks/useSubscriptionLimits";
import { toast } from "sonner";

interface ProductActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filters: ProductFilterState;
  onFilterChange: (filters: ProductFilterState) => void;
  categories?: Array<{ id: string; name: string }>;
  availableTags?: string[];
  onRefresh?: () => void; // Add refresh callback for after import
}

export const ProductActions: React.FC<ProductActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  filters,
  onFilterChange,
  categories,
  availableTags,
  onRefresh,
}) => {
  const {
    canCreateProduct,
    productMessage,
    currentProductUsage,
    productLimit,
    isLoading: limitsLoading,
  } = useProductLimits();

  const handleAddProductClick = (e: React.MouseEvent) => {
    if (!canCreateProduct) {
      e.preventDefault();
      toast.error(productMessage || "Batas produk tercapai untuk paket Anda.");
      return;
    }
  };
  return (
    <div className="flex flex-wrap items-center justify-between gap-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer">
            <AdjustmentsHorizontalIcon className="mr-2 h-5 w-5" />
            Kolom
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-56"
            onCloseAutoFocus={(e) => e.preventDefault()}
            sideOffset={5}
            align="start"
          >
            <DropdownMenuLabel>Pilih Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
              {productsColumnConfig.map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.key}
                  checked={columnVisibility[column.key]}
                  onCheckedChange={(checked) =>
                    setColumnVisibility((prev) => ({
                      ...prev,
                      [column.key]: !!checked,
                    }))
                  }
                  onSelect={(e) => e.preventDefault()}
                >
                  {column.label}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Component */}
        <ProductFilter
          filters={filters}
          onFilterChange={onFilterChange}
          categories={categories}
          availableTags={availableTags}
        />

        {/* Import/Export Component */}
        <ProductImportExport onRefresh={onRefresh} />
      </div>

      <div className="flex items-center gap-2 w-full sm:w-auto">
        {/* Search Input */}
        <div className="relative flex-grow">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400 dark:text-gray-500"
              aria-hidden="true"
            />
          </div>
          <input
            type="text"
            placeholder="Cari Produk"
            className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-10 pr-3 leading-5 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-indigo-500 dark:focus:border-indigo-400 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:focus:ring-indigo-400 sm:text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Add Product Button */}
        {canCreateProduct ? (
          <Link
            href="/dashboard/products/new"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
          >
            <PlusIcon className="mr-2 h-5 w-5" />
            Tambah
          </Link>
        ) : (
          <Button
            disabled
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm cursor-not-allowed"
            onClick={handleAddProductClick}
            title={productMessage || "Batas produk tercapai"}
          >
            <Lock className="mr-2 h-5 w-5" />
            Tambah
          </Button>
        )}
      </div>
    </div>
  );
};
