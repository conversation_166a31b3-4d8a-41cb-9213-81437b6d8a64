"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Percent, Calendar, Package, Users } from "lucide-react";
import { toast } from "sonner";
import { getEventDiscounts } from "@/actions/event-discounts";
import EventDiscountTable from "./EventDiscountTable";
import EventDiscountDialog from "./EventDiscountDialog";
import { EventDiscount, transformEventDiscounts } from "./types";
import DashboardLayout from "@/components/layout/dashboardlayout";
import Head from "next/head";

const EventDiscountManagement: React.FC = () => {
  const [eventDiscounts, setEventDiscounts] = useState<EventDiscount[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingDiscount, setEditingDiscount] = useState<EventDiscount | null>(
    null
  );

  // Fetch event discounts
  const fetchEventDiscounts = async () => {
    try {
      setLoading(true);
      const result = await getEventDiscounts();
      if (result.success && result.data) {
        // Transform the raw data to match our component interface
        const transformedData = transformEventDiscounts(result.data as any);
        setEventDiscounts(transformedData);
      } else {
        toast.error(result.error || "Failed to fetch event discounts");
      }
    } catch (error) {
      console.error("Error fetching event discounts:", error);
      toast.error("Failed to fetch event discounts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEventDiscounts();
  }, []);

  const handleCreateNew = () => {
    setEditingDiscount(null);
    setDialogOpen(true);
  };

  const handleEdit = (discount: EventDiscount) => {
    setEditingDiscount(discount);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setEditingDiscount(null);
  };

  const handleSuccess = () => {
    fetchEventDiscounts();
    handleDialogClose();
  };

  // Calculate statistics
  const totalDiscounts = eventDiscounts.length;
  const activeDiscounts = eventDiscounts.filter((d) => d.isActive).length;
  const totalProductsWithDiscounts = eventDiscounts.reduce(
    (sum, d) => sum + d._count.products,
    0
  );
  const totalSalesWithDiscounts = eventDiscounts.reduce(
    (sum, d) => sum + d._count.sales,
    0
  );

  return (
    <DashboardLayout>
      <Head>
        <title>Diskon Event - KivaPOS</title>
      </Head>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Diskon Event</h1>
            <p className="text-muted-foreground">
              Kelola diskon event untuk produk Anda
            </p>
          </div>
          <Button
            onClick={handleCreateNew}
            className="flex items-center gap-2 cursor-pointer"
          >
            <Plus className="h-4 w-4" />
            Buat Diskon Event
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Total Diskon */}
          <div className="rounded-lg border border-gray-200 bg-blue-50 dark:border-gray-700 dark:bg-blue-900/20">
            <div className="flex justify-between items-center p-4">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Total Diskon
              </div>
              <Percent className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            </div>
            <div className="px-4 pb-4">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Event diskon
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {totalDiscounts}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {activeDiscounts} aktif
              </p>
            </div>
          </div>

          {/* Diskon Aktif */}
          <div className="rounded-lg border border-gray-200 bg-green-50 dark:border-gray-700 dark:bg-green-900/20">
            <div className="flex justify-between items-center p-4">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Diskon Aktif
              </div>
              <Calendar className="h-5 w-5 text-green-500 dark:text-green-400" />
            </div>
            <div className="px-4 pb-4">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Sedang berjalan
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {activeDiscounts}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                dari {totalDiscounts} total
              </p>
            </div>
          </div>

          {/* Produk Terdiskon */}
          <div className="rounded-lg border border-gray-200 bg-amber-50 dark:border-gray-700 dark:bg-amber-900/20">
            <div className="flex justify-between items-center p-4">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Produk Terdiskon
              </div>
              <Package className="h-5 w-5 text-amber-500 dark:text-amber-400" />
            </div>
            <div className="px-4 pb-4">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Total produk
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {totalProductsWithDiscounts}
              </div>
            </div>
          </div>

          {/* Penjualan Terdiskon */}
          <div className="rounded-lg border border-gray-200 bg-purple-50 dark:border-gray-700 dark:bg-purple-900/20">
            <div className="flex justify-between items-center p-4">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Penjualan Terdiskon
              </div>
              <Users className="h-5 w-5 text-purple-500 dark:text-purple-400" />
            </div>
            <div className="px-4 pb-4">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Total transaksi
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {totalSalesWithDiscounts}
              </div>
            </div>
          </div>
        </div>

        {/* Event Discounts Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Diskon Event</CardTitle>
          </CardHeader>
          <CardContent>
            <EventDiscountTable
              eventDiscounts={eventDiscounts}
              loading={loading}
              onEdit={handleEdit}
              onRefresh={fetchEventDiscounts}
            />
          </CardContent>
        </Card>

        {/* Create/Edit Dialog */}
        <EventDiscountDialog
          open={dialogOpen}
          onClose={handleDialogClose}
          onSuccess={handleSuccess}
          editingDiscount={editingDiscount}
        />
      </div>
    </DashboardLayout>
  );
};

export default EventDiscountManagement;
