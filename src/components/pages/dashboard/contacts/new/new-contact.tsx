"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowLeft,
  ChevronDown,
  Plus,
  Users,
  Store,
  Building2,
  Info,
  Trash2,
  Edit3,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";
import { addCustomer } from "@/actions/entities/customers";
import { addSupplier } from "@/actions/entities/suppliers";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useContactLimits } from "@/hooks/useSubscriptionLimits";

interface BankAccount {
  id: string;
  bankName: string;
  bankBranch: string;
  accountHolder: string;
  accountNumber: string;
  isEditing: boolean;
}

const NewContactPage: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Subscription limits
  const {
    canCreateContact,
    contactMessage,
    currentContactUsage,
    contactLimit,
    isLoading: limitsLoading,
  } = useContactLimits();
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([
    {
      id: "1",
      bankName: "",
      bankBranch: "",
      accountHolder: "",
      accountNumber: "",
      isEditing: true,
    },
  ]);
  const [formData, setFormData] = useState({
    displayName: "",
    contactGroup: "" as "customer" | "supplier" | "",
    firstName: "",
    middleName: "",
    lastName: "",
    phone: "",
    identityType: "",
    identityNumber: "",
    email: "",
    otherInfo: "",
    companyName: "",
    telephone: "",
    fax: "",
    NIK: "",
    NPWP: "",
    billingAddress: "",
    shippingAddress: "",
    sameAsShipping: false,
    notes: "",
  });

  // Check subscription limits on component mount
  useEffect(() => {
    if (!limitsLoading && !canCreateContact) {
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      router.push("/dashboard/contacts");
    }
  }, [canCreateContact, contactMessage, limitsLoading, router]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleBankInputChange = (
    accountId: string,
    field: string,
    value: string
  ) => {
    setBankAccounts((prev) =>
      prev.map((account) =>
        account.id === accountId ? { ...account, [field]: value } : account
      )
    );
  };

  const addBankAccount = () => {
    const newId = (bankAccounts.length + 1).toString();
    setBankAccounts((prev) => [
      ...prev,
      {
        id: newId,
        bankName: "",
        bankBranch: "",
        accountHolder: "",
        accountNumber: "",
        isEditing: true,
      },
    ]);
  };

  const saveBankAccount = (accountId: string) => {
    setBankAccounts((prev) =>
      prev.map((account) =>
        account.id === accountId ? { ...account, isEditing: false } : account
      )
    );
  };

  const editBankAccount = (accountId: string) => {
    setBankAccounts((prev) =>
      prev.map((account) =>
        account.id === accountId ? { ...account, isEditing: true } : account
      )
    );
  };

  const removeBankAccount = (accountId: string) => {
    setBankAccounts((prev) =>
      prev.filter((account) => account.id !== accountId)
    );
  };

  const handleSubmit = async () => {
    // Double-check subscription limits before submission
    if (!canCreateContact) {
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      return;
    }

    if (!formData.displayName.trim()) {
      toast.error("Nama tampilan wajib diisi!");
      return;
    }

    if (!formData.contactGroup) {
      toast.error("Grup kontak wajib dipilih!");
      return;
    }

    setIsLoading(true);
    try {
      let result;

      if (formData.contactGroup === "customer") {
        // Use the enhanced customer creation approach
        const customerData = {
          name: formData.displayName,
          firstName: formData.firstName || undefined,
          middleName: formData.middleName || undefined,
          lastName: formData.lastName || undefined,
          contactName:
            formData.firstName && formData.lastName
              ? `${formData.firstName} ${formData.lastName}`.trim()
              : formData.firstName || formData.lastName || undefined,
          phone: formData.phone || undefined,
          telephone: formData.telephone || undefined,
          fax: formData.fax || undefined,
          email: formData.email || "",
          identityType: formData.identityType || undefined,
          identityNumber: formData.identityNumber || undefined,
          NIK: formData.NIK || undefined,
          NPWP: formData.NPWP || undefined,
          companyName: formData.companyName || undefined,
          otherInfo: formData.otherInfo || undefined,
          address: formData.billingAddress || undefined, // Keep for backward compatibility
          billingAddress: formData.billingAddress || undefined,
          shippingAddress: formData.shippingAddress || undefined,
          sameAsShipping: formData.sameAsShipping,
          bankName: bankAccounts[0]?.bankName || undefined,
          bankBranch: bankAccounts[0]?.bankBranch || undefined,
          accountHolder: bankAccounts[0]?.accountHolder || undefined,
          accountNumber: bankAccounts[0]?.accountNumber || undefined,
          notes: formData.notes || undefined,
        };

        result = await addCustomer(customerData);

        if (result.success) {
          toast.success(result.success);
          router.push("/dashboard/contacts?tab=pelanggan");
        } else {
          toast.error(result.error || "Gagal menambahkan pelanggan");
        }
      } else if (formData.contactGroup === "supplier") {
        // Use the enhanced supplier creation approach
        const supplierData = {
          name: formData.displayName,
          firstName: formData.firstName || undefined,
          middleName: formData.middleName || undefined,
          lastName: formData.lastName || undefined,
          contactName:
            formData.firstName && formData.lastName
              ? `${formData.firstName} ${formData.lastName}`.trim()
              : formData.firstName || formData.lastName || undefined,
          phone: formData.phone || undefined,
          telephone: formData.telephone || undefined,
          fax: formData.fax || undefined,
          email: formData.email || "",
          identityType: formData.identityType || undefined,
          identityNumber: formData.identityNumber || undefined,
          NIK: formData.NIK || undefined,
          NPWP: formData.NPWP || undefined,
          companyName: formData.companyName || undefined,
          otherInfo: formData.otherInfo || undefined,
          address: formData.billingAddress || undefined, // Keep for backward compatibility
          billingAddress: formData.billingAddress || undefined,
          shippingAddress: formData.shippingAddress || undefined,
          sameAsShipping: formData.sameAsShipping,
          bankName: bankAccounts[0]?.bankName || undefined,
          bankBranch: bankAccounts[0]?.bankBranch || undefined,
          accountHolder: bankAccounts[0]?.accountHolder || undefined,
          accountNumber: bankAccounts[0]?.accountNumber || undefined,
          notes: formData.notes || undefined,
        };

        result = await addSupplier(supplierData);

        if (result.success) {
          toast.success(result.success);
          router.push("/dashboard/contacts?tab=supplier");
        } else {
          toast.error(result.error || "Gagal menambahkan supplier");
        }
      } else {
        toast.error("Grup kontak tidak valid!");
        return;
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Terjadi kesalahan saat menyimpan kontak");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 cursor-pointer"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Kontak baru
            </h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="max-w-[900px]">
          {/* Subscription Limit Warning */}
          {contactLimit && currentContactUsage !== undefined && (
            <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 mb-6">
              <CardContent className="pt-0">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                      Batas Kontak
                    </p>
                    <p className="text-sm text-orange-700 dark:text-orange-300">
                      Anda telah menggunakan {currentContactUsage} dari{" "}
                      {contactLimit} kontak yang tersedia.
                      {contactLimit - currentContactUsage <= 5 && (
                        <span className="font-medium">
                          {" "}
                          Sisa {contactLimit - currentContactUsage} kontak lagi.
                        </span>
                      )}
                    </p>
                    {contactLimit - currentContactUsage <= 0 && (
                      <p className="text-sm text-orange-800 dark:text-orange-200 font-medium">
                        Batas kontak telah tercapai. Upgrade paket Anda untuk
                        menambah lebih banyak kontak.
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info Kontak Section */}
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">
                  👤
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Info kontak
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Display Name */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Nama Tampilan <span className="text-red-500">*</span>
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Ketik nama kontak..."
                  value={formData.displayName}
                  onChange={(e) =>
                    handleInputChange("displayName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Contact Group */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Grup Kontak
              </label>
              <div className="col-span-3">
                <div className="flex gap-6">
                  <label className="flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <input
                      type="radio"
                      name="contactGroup"
                      value="customer"
                      checked={formData.contactGroup === "customer"}
                      onChange={(e) =>
                        handleInputChange("contactGroup", e.target.value)
                      }
                      className="mr-3 text-blue-600 focus:ring-blue-500"
                    />
                    <Users className="h-4 w-4 mr-2 text-blue-600" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Pelanggan
                    </span>
                  </label>
                  <label className="flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <input
                      type="radio"
                      name="contactGroup"
                      value="supplier"
                      checked={formData.contactGroup === "supplier"}
                      onChange={(e) =>
                        handleInputChange("contactGroup", e.target.value)
                      }
                      className="mr-3 text-blue-600 focus:ring-blue-500"
                    />
                    <Store className="h-4 w-4 mr-2 text-green-600" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Supplier
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Info Umum Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">
                  📋
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Info umum
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Name Fields */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Nama lengkap
              </label>
              <div className="col-span-3 grid grid-cols-3 gap-4">
                <div>
                  <input
                    type="text"
                    placeholder="Nama depan"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Nama tengah"
                    value={formData.middleName}
                    onChange={(e) =>
                      handleInputChange("middleName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Nama belakang"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>

            {/* Phone */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nomor handphone
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: 081 2 3374 5678"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Identity */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Identitas
              </label>
              <div className="col-span-3">
                <div className="flex">
                  <select
                    value={formData.identityType}
                    onChange={(e) =>
                      handleInputChange("identityType", e.target.value)
                    }
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">-</option>
                    <option value="ktp">KTP</option>
                    <option value="sim">SIM</option>
                    <option value="passport">Passport</option>
                  </select>
                  <input
                    type="text"
                    placeholder="Nomor ID"
                    value={formData.identityNumber}
                    onChange={(e) =>
                      handleInputChange("identityNumber", e.target.value)
                    }
                    className="flex-1 px-3 py-2 border-t border-r border-b border-gray-300 dark:border-gray-600 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>

            {/* Email */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Email
              </label>
              <div className="col-span-3">
                <input
                  type="email"
                  placeholder="Enter email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Company Name */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nama Perusahaan
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Nama perusahaan"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange("companyName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Nomor telepon */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nomor telepon
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: (021) 3456789"
                  value={formData.telephone}
                  onChange={(e) =>
                    handleInputChange("telephone", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Fax */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Fax
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: 0812 3456 7890"
                  value={formData.fax}
                  onChange={(e) => handleInputChange("fax", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* NPWP */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                NPWP
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="NPWP"
                  value={formData.NPWP}
                  onChange={(e) => handleInputChange("NPWP", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Billing Address */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Alamat penagihan
              </label>
              <div className="col-span-3">
                <textarea
                  placeholder="e.g. Jalan Indonesia Blok L No. 22"
                  rows={3}
                  value={formData.billingAddress}
                  onChange={(e) =>
                    handleInputChange("billingAddress", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>
            </div>

            {/* Shipping Address */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Alamat pengiriman
              </label>
              <div className="col-span-3">
                <div className="flex items-center gap-3 mb-4">
                  <input
                    type="checkbox"
                    checked={formData.sameAsShipping}
                    onChange={(e) =>
                      handleInputChange("sameAsShipping", e.target.checked)
                    }
                    className="mr-2 cursor-pointer"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Samakan dengan alamat penagihan
                  </span>
                </div>

                {/* Shipping Address Form - Only show if not same as billing */}
                {!formData.sameAsShipping && (
                  <div>
                    <textarea
                      placeholder="Masukkan alamat pengiriman"
                      rows={3}
                      value={formData.shippingAddress}
                      onChange={(e) =>
                        handleInputChange("shippingAddress", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    ></textarea>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Info Bank Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">
                  🏦
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Info bank
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Bank Accounts */}
            <TooltipProvider>
              {bankAccounts.map((account, index) => (
                <div key={account.id} className="mb-6 p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 flex items-center">
                      <Building2 className="h-4 w-4 mr-2 text-blue-600" />
                      Akun Bank {index + 1}
                    </h4>
                    <div className="flex items-center gap-2">
                      {!account.isEditing && (
                        <>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => editBankAccount(account.id)}
                            className="flex items-center gap-1 text-xs cursor-pointer"
                          >
                            <Edit3 className="h-3 w-3" />
                            Edit
                          </Button>
                          {bankAccounts.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeBankAccount(account.id)}
                              className="flex items-center gap-1 text-xs text-red-600 hover:text-red-700 cursor-pointer"
                            >
                              <Trash2 className="h-3 w-3" />
                              Hapus
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {account.isEditing ? (
                    <div className="space-y-4">
                      {/* Bank Name */}
                      <div className="grid grid-cols-4 gap-4 items-center">
                        <label className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          Nama bank
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">
                                Masukkan nama bank lengkap (contoh: Bank Central
                                Asia, Bank Mandiri)
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </label>
                        <div className="col-span-3">
                          <input
                            type="text"
                            placeholder="Contoh: Bank Central Asia"
                            value={account.bankName}
                            onChange={(e) =>
                              handleBankInputChange(
                                account.id,
                                "bankName",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      </div>

                      {/* Bank Branch */}
                      <div className="grid grid-cols-4 gap-4 items-center">
                        <label className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          Kantor cabang
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">
                                Masukkan nama kantor cabang bank (contoh:
                                Jakarta Pusat, Surabaya Kota)
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </label>
                        <div className="col-span-3">
                          <input
                            type="text"
                            placeholder="Contoh: Jakarta Pusat"
                            value={account.bankBranch}
                            onChange={(e) =>
                              handleBankInputChange(
                                account.id,
                                "bankBranch",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      </div>

                      {/* Account Holder */}
                      <div className="grid grid-cols-4 gap-4 items-center">
                        <label className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          Pemegang akun bank
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">
                                Nama lengkap pemegang rekening sesuai dengan
                                yang tertera di buku tabungan
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </label>
                        <div className="col-span-3">
                          <input
                            type="text"
                            placeholder="Nama lengkap pemegang rekening"
                            value={account.accountHolder}
                            onChange={(e) =>
                              handleBankInputChange(
                                account.id,
                                "accountHolder",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      </div>

                      {/* Account Number */}
                      <div className="grid grid-cols-4 gap-4 items-center">
                        <label className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          Nomor rekening
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">
                                Nomor rekening bank tanpa spasi atau tanda baca
                                (contoh: **********)
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </label>
                        <div className="col-span-3">
                          <input
                            type="text"
                            placeholder="Contoh: **********"
                            value={account.accountNumber}
                            onChange={(e) =>
                              handleBankInputChange(
                                account.id,
                                "accountNumber",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      </div>

                      {/* Save Bank Button */}
                      <div className="flex justify-end">
                        <Button
                          type="button"
                          onClick={() => saveBankAccount(account.id)}
                          className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
                        >
                          Simpan Bank
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div>
                        <strong>Bank:</strong> {account.bankName || "-"}
                      </div>
                      <div>
                        <strong>Cabang:</strong> {account.bankBranch || "-"}
                      </div>
                      <div>
                        <strong>Pemegang:</strong>{" "}
                        {account.accountHolder || "-"}
                      </div>
                      <div>
                        <strong>Nomor:</strong> {account.accountNumber || "-"}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </TooltipProvider>

            <div className="mb-4">
              <Button
                type="button"
                variant="outline"
                onClick={addBankAccount}
                className="flex items-center gap-2 cursor-pointer"
              >
                <Plus className="h-4 w-4" />
                Tambah bank lain
              </Button>
            </div>
          </div>

          {/* Notes Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">
                  📝
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Catatan
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            <div className="grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Catatan tambahan
              </label>
              <div className="col-span-3">
                <textarea
                  placeholder="Masukkan catatan tambahan tentang kontak ini..."
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Action Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex justify-end space-x-4 max-w-[900px] pl-6">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="cursor-pointer"
          >
            Batalkan
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
          >
            {isLoading ? "Menyimpan..." : "Simpan"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewContactPage;
