import { db } from "@/lib/prisma";
import { SubscriptionPlan } from "@prisma/client";
import { SUBSCRIPTION_PLANS, getEffectivePlan } from "@/lib/subscription";
import { startOfMonth, endOfMonth } from "date-fns";

// Types for subscription limits
export interface SubscriptionLimits {
  maxProducts: number | null;
  maxTransactionsPerMonth: number | null;
  maxUsers: number | null;
  maxContacts: number | null;
  notifications: {
    website: boolean;
    email: boolean;
  };
  backup: {
    manual: boolean;
    automatic: boolean;
    frequencies: string[];
  };
  support: {
    priority: boolean;
    developer: boolean;
  };
}

export interface UsageCounts {
  products: number;
  transactionsThisMonth: number;
  users: number; // Owner + employees
  contacts: number; // Suppliers + customers
}

export interface LimitCheckResult {
  allowed: boolean;
  message?: string;
  currentUsage?: number;
  limit?: number | null;
}

/**
 * Get subscription limits for a given plan
 */
export function getSubscriptionLimits(
  plan: SubscriptionPlan
): SubscriptionLimits {
  const planDetails = SUBSCRIPTION_PLANS[plan];
  return planDetails.limits;
}

/**
 * Get current usage counts for a user
 */
export async function getCurrentUsage(userId: string): Promise<UsageCounts> {
  const now = new Date();
  const monthStart = startOfMonth(now);
  const monthEnd = endOfMonth(now);

  // Get product count
  const productCount = await db.product.count({
    where: { userId },
  });

  // Get transaction count for this month (purchases + sales + services)
  const [purchaseCount, saleCount, serviceCount] = await Promise.all([
    db.purchase.count({
      where: {
        userId,
        createdAt: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
    }),
    db.sale.count({
      where: {
        userId,
        createdAt: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
    }),
    db.service.count({
      where: {
        userId,
        createdAt: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
    }),
  ]);

  const transactionsThisMonth = purchaseCount + saleCount + serviceCount;

  // Get user count (owner + employees)
  const employeeCount = await db.employee.count({
    where: { ownerId: userId },
  });
  const users = 1 + employeeCount; // 1 owner + employees

  // Get contact count (suppliers + customers)
  const [supplierCount, customerCount] = await Promise.all([
    db.supplier.count({
      where: { userId },
    }),
    db.customer.count({
      where: { userId },
    }),
  ]);

  const contacts = supplierCount + customerCount;

  return {
    products: productCount,
    transactionsThisMonth,
    users,
    contacts,
  };
}

/**
 * Check if user can create a new product
 */
export async function canCreateProduct(
  userId: string,
  userPlan: SubscriptionPlan
): Promise<LimitCheckResult> {
  // Get user data to check trial status
  const user = await db.user.findUnique({
    where: { id: userId },
    select: {
      currentPlan: true,
      trialStartDate: true,
      trialEndDate: true,
      isTrialActive: true,
    },
  });

  if (!user) {
    return { allowed: false, message: "User not found" };
  }

  // Get effective plan (considering trial status)
  const effectivePlan = getEffectivePlan(user);
  const limits = getSubscriptionLimits(effectivePlan);

  if (limits.maxProducts === null) {
    return { allowed: true }; // Unlimited
  }

  const usage = await getCurrentUsage(userId);

  if (usage.products >= limits.maxProducts) {
    return {
      allowed: false,
      message: `Anda telah mencapai batas maksimal ${limits.maxProducts} produk untuk ${SUBSCRIPTION_PLANS[effectivePlan].name}. Upgrade paket untuk menambah lebih banyak produk.`,
      currentUsage: usage.products,
      limit: limits.maxProducts,
    };
  }

  return {
    allowed: true,
    currentUsage: usage.products,
    limit: limits.maxProducts,
  };
}

/**
 * Check if user can create a new transaction (purchase/sale/service)
 */
export async function canCreateTransaction(
  userId: string,
  userPlan: SubscriptionPlan
): Promise<LimitCheckResult> {
  const limits = getSubscriptionLimits(userPlan);

  if (limits.maxTransactionsPerMonth === null) {
    return { allowed: true }; // Unlimited
  }

  const usage = await getCurrentUsage(userId);

  if (usage.transactionsThisMonth >= limits.maxTransactionsPerMonth) {
    return {
      allowed: false,
      message: `Anda telah mencapai batas maksimal ${limits.maxTransactionsPerMonth} transaksi per bulan untuk ${SUBSCRIPTION_PLANS[userPlan].name}. Upgrade paket untuk menambah lebih banyak transaksi.`,
      currentUsage: usage.transactionsThisMonth,
      limit: limits.maxTransactionsPerMonth,
    };
  }

  return {
    allowed: true,
    currentUsage: usage.transactionsThisMonth,
    limit: limits.maxTransactionsPerMonth,
  };
}

/**
 * Check if user can create a new employee
 */
export async function canCreateEmployee(
  userId: string,
  userPlan: SubscriptionPlan
): Promise<LimitCheckResult> {
  const limits = getSubscriptionLimits(userPlan);

  if (limits.maxUsers === null) {
    return { allowed: true }; // Unlimited
  }

  const usage = await getCurrentUsage(userId);

  if (usage.users >= limits.maxUsers) {
    return {
      allowed: false,
      message: `Anda telah mencapai batas maksimal ${limits.maxUsers} pengguna untuk ${SUBSCRIPTION_PLANS[userPlan].name}. Upgrade paket untuk menambah lebih banyak pengguna.`,
      currentUsage: usage.users,
      limit: limits.maxUsers,
    };
  }

  return { allowed: true, currentUsage: usage.users, limit: limits.maxUsers };
}

/**
 * Check if user can create a new contact (supplier/customer)
 */
export async function canCreateContact(
  userId: string,
  userPlan: SubscriptionPlan
): Promise<LimitCheckResult> {
  const limits = getSubscriptionLimits(userPlan);

  if (limits.maxContacts === null) {
    return { allowed: true }; // Unlimited
  }

  const usage = await getCurrentUsage(userId);

  if (usage.contacts >= limits.maxContacts) {
    return {
      allowed: false,
      message: `Anda telah mencapai batas maksimal ${limits.maxContacts} kontak untuk ${SUBSCRIPTION_PLANS[userPlan].name}. Upgrade paket untuk menambah lebih banyak kontak.`,
      currentUsage: usage.contacts,
      limit: limits.maxContacts,
    };
  }

  return {
    allowed: true,
    currentUsage: usage.contacts,
    limit: limits.maxContacts,
  };
}

/**
 * Check if user has access to notifications
 */
export function hasNotificationAccess(userPlan: SubscriptionPlan): boolean {
  const limits = getSubscriptionLimits(userPlan);
  return limits.notifications.website;
}

/**
 * Check if user has access to email notifications
 */
export function hasEmailNotificationAccess(
  userPlan: SubscriptionPlan
): boolean {
  const limits = getSubscriptionLimits(userPlan);
  return limits.notifications.email;
}

/**
 * Get a summary of current usage vs limits
 */
export async function getUsageSummary(
  userId: string,
  userPlan: SubscriptionPlan
) {
  const limits = getSubscriptionLimits(userPlan);
  const usage = await getCurrentUsage(userId);

  return {
    plan: SUBSCRIPTION_PLANS[userPlan].name,
    products: {
      current: usage.products,
      limit: limits.maxProducts,
      percentage: limits.maxProducts
        ? Math.round((usage.products / limits.maxProducts) * 100)
        : 0,
    },
    transactions: {
      current: usage.transactionsThisMonth,
      limit: limits.maxTransactionsPerMonth,
      percentage: limits.maxTransactionsPerMonth
        ? Math.round(
            (usage.transactionsThisMonth / limits.maxTransactionsPerMonth) * 100
          )
        : 0,
    },
    users: {
      current: usage.users,
      limit: limits.maxUsers,
      percentage: limits.maxUsers
        ? Math.round((usage.users / limits.maxUsers) * 100)
        : 0,
    },
    contacts: {
      current: usage.contacts,
      limit: limits.maxContacts,
      percentage: limits.maxContacts
        ? Math.round((usage.contacts / limits.maxContacts) * 100)
        : 0,
    },
    features: {
      notifications: limits.notifications,
      backup: limits.backup,
      support: limits.support,
    },
  };
}
