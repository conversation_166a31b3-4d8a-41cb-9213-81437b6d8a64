import * as XLSX from "xlsx-js-style";
import { CELL_STYLES, NUMBER_FORMATS } from "./excelStyles";

// Create Excel import template for products
export const createProductImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns (excluding ID Product and Total Stok as per requirements)
  const columns = [
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Deskripsi",
      label: "Deskripsi",
      required: false,
      type: "text",
      example:
        "Kopi arabica premium berkualitas tinggi dengan cita rasa yang khas",
    },
    {
      key: "Kode Produk",
      label: "Kode Produk (SKU)",
      required: false,
      type: "text",
      example: "KAP-001",
    },
    {
      key: "Barcode",
      label: "Barcode",
      required: false,
      type: "text",
      example: "1234567890123",
    },
    {
      key: "Kategori",
      label: "Kategori",
      required: false,
      type: "text",
      example: "Minuman",
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Beli",
      label: "Harga Beli",
      required: false,
      type: "currency",
      example: 50000,
    },
    {
      key: "Harga Jual",
      label: "Harga Jual",
      required: true,
      type: "currency",
      example: 75000,
    },
    {
      key: "Harga Grosir",
      label: "Harga Grosir",
      required: false,
      type: "currency",
      example: 65000,
    },
    {
      key: "Harga Diskon",
      label: "Harga Diskon",
      required: false,
      type: "currency",
      example: 60000,
    },
    {
      key: "Tag Produk",
      label: "Tag Produk",
      required: false,
      type: "text",
      example: "premium,organik,bestseller",
    },
    {
      key: "Varian Warna",
      label: "Varian Warna",
      required: false,
      type: "text",
      example: "Merah,Biru,Hijau",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "Teh Hijau Organik",
      "Teh hijau organik pilihan dengan antioksidan tinggi untuk kesehatan",
      "THO-002",
      "2345678901234",
      "Minuman",
      "Box",
      30000,
      45000,
      40000,
      35000,
      "organik,sehat",
      "Original,Mint",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    const headerStyle = {
      ...CELL_STYLES.tableHeader,
      fill: {
        fgColor: { rgb: col.required ? "E6F3FF" : "E6F3FF" }, // Red for required, blue for optional
      },
    };
    applyCellStyle(worksheet, cellRef, headerStyle);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    for (let row = 1; row <= 2; row++) {
      const cellRef = XLSX.utils.encode_cell({ r: row, c: index });
      const exampleStyle = {
        ...CELL_STYLES.tableDataEven,
        fill: { fgColor: { rgb: "F0F8FF" } }, // Light blue for examples
        font: { ...CELL_STYLES.tableDataEven.font, italic: true },
      };
      applyCellStyle(worksheet, cellRef, exampleStyle);

      // Apply currency format for currency columns
      if (col.type === "currency" && worksheet[cellRef]) {
        worksheet[cellRef].z = NUMBER_FORMATS.currency;
      }
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Produk");

  // Create info document sheet
  const infoDocumentSheet = createInfoDocumentSheet();
  XLSX.utils.book_append_sheet(workbook, infoDocumentSheet, "Info Dokumen");

  return workbook;
};

// Create info document sheet
const createInfoDocumentSheet = (): XLSX.WorkSheet => {
  const infoData = [
    ["INFORMASI DOKUMEN TEMPLATE IMPORT PRODUK"],
    [""],
    ["TENTANG DOKUMEN:"],
    ["• Nama File: Template Import Produk"],
    ["• Versi: 1.0"],
    ["• Tanggal Dibuat: " + new Date().toLocaleDateString("id-ID")],
    ["• Format: Microsoft Excel (.xlsx)"],
    ["• Tujuan: Import data produk secara massal ke sistem"],
    [""],
    ["DESKRIPSI:"],
    ["Template ini dirancang khusus untuk memudahkan proses import"],
    ["data produk dalam jumlah besar ke dalam sistem manajemen"],
    ["inventori. Template ini mendukung berbagai jenis data produk"],
    ["termasuk informasi dasar, harga, kategori, dan varian."],
    [""],
    ["FITUR UTAMA:"],
    ["• Import hingga 1000 produk sekaligus"],
    ["• Validasi data otomatis"],
    ["• Pembuatan kategori dan satuan otomatis"],
    ["• Dukungan untuk varian warna dan tag produk"],
    ["• Format harga yang fleksibel"],
    ["• Penanganan duplikasi data"],
    [""],
    ["STRUKTUR TEMPLATE:"],
    ["1. Sheet 'Template Produk': Berisi format data untuk diisi"],
    ["2. Sheet 'Petunjuk Penggunaan': Panduan lengkap penggunaan"],
    ["3. Sheet 'Info Dokumen': Informasi tentang template ini"],
    [""],
    ["KEAMANAN DATA:"],
    ["• Data yang diimport akan dienkripsi selama proses"],
    ["• Backup otomatis dibuat sebelum import"],
    ["• Log aktivitas tersimpan untuk audit"],
    ["• Validasi integritas data sebelum penyimpanan"],
    [""],
    ["DUKUNGAN:"],
    ["Jika mengalami kesulitan dalam menggunakan template ini,"],
    ["silakan hubungi tim support melalui:"],
    ["• Email: <EMAIL>"],
    ["• WhatsApp: +62 812-3456-7890"],
    ["• Live Chat: Tersedia di aplikasi"],
    [""],
    ["CATATAN PENTING:"],
    ["• Pastikan format data sesuai dengan contoh yang diberikan"],
    ["• Jangan mengubah struktur header pada template"],
    ["• Simpan file dalam format Excel (.xlsx) sebelum upload"],
    ["• Maksimal ukuran file: 10MB"],
    ["• Disarankan untuk melakukan backup data sebelum import"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(infoData);

  // Apply styles
  applyCellStyle(worksheet, "A1", {
    ...CELL_STYLES.reportTitle,
    font: { ...CELL_STYLES.reportTitle.font, size: 16, bold: true },
  });

  // Style section headers
  const sectionHeaders = [3, 10, 16, 24, 29, 35, 42];
  sectionHeaders.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
  });

  // Set column width
  setColumnWidths(worksheet, [100]);

  return worksheet;
};

// Utility functions (same as in excelTemplate.ts)
const applyCellStyle = (
  worksheet: XLSX.WorkSheet,
  cellRef: string,
  style: any
) => {
  if (!worksheet[cellRef]) {
    worksheet[cellRef] = { t: "s", v: "" };
  }
  worksheet[cellRef].s = style;
};

const setColumnWidths = (worksheet: XLSX.WorkSheet, widths: number[]) => {
  worksheet["!cols"] = widths.map((width) => ({ width }));
};

const addAutoFilter = (worksheet: XLSX.WorkSheet, range: string) => {
  worksheet["!autofilter"] = { ref: range };
};

const freezePanes = (worksheet: XLSX.WorkSheet, cell: string) => {
  worksheet["!freeze"] = XLSX.utils.decode_cell(cell);
};

// Create Excel import template for purchases
export const createPurchaseImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns for purchases
  const columns = [
    {
      key: "Tanggal Pembelian",
      label: "Tanggal Pembelian",
      required: true,
      type: "date",
      example: "2024-01-15",
    },
    {
      key: "Supplier",
      label: "Supplier",
      required: false,
      type: "text",
      example: "PT Supplier Utama",
    },
    {
      key: "No. Invoice",
      label: "No. Invoice",
      required: false,
      type: "text",
      example: "INV-2024-001",
    },
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Quantity",
      label: "Quantity",
      required: true,
      type: "number",
      example: 10,
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Beli",
      label: "Harga Beli",
      required: true,
      type: "currency",
      example: 50000,
    },
    {
      key: "Diskon (%)",
      label: "Diskon (%)",
      required: false,
      type: "number",
      example: 5,
    },
    {
      key: "Diskon (Rp)",
      label: "Diskon (Rp)",
      required: false,
      type: "currency",
      example: 2500,
    },
    {
      key: "PPN (%)",
      label: "PPN (%)",
      required: false,
      type: "number",
      example: 11,
    },
    {
      key: "Gudang",
      label: "Gudang",
      required: false,
      type: "text",
      example: "Gudang Utama",
    },
    {
      key: "Memo",
      label: "Memo",
      required: false,
      type: "text",
      example: "Pembelian rutin bulanan",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "2024-01-16",
      "CV Distributor Jaya",
      "INV-2024-002",
      "Teh Hijau Organik",
      25,
      "Box",
      30000,
      10,
      3000,
      11,
      "Gudang Cabang",
      "Stok untuk promosi",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, CELL_STYLES.tableHeader);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 1, c: index });
    if (col.type === "currency" || col.type === "number") {
      applyCellStyle(worksheet, cellRef, {
        ...CELL_STYLES.tableDataEven,
        numFmt:
          col.type === "currency"
            ? NUMBER_FORMATS.currency
            : NUMBER_FORMATS.integer,
      });
    } else {
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Pembelian");

  return workbook;
};

// Create Excel import template for sales
export const createSalesImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns for sales
  const columns = [
    {
      key: "Tanggal Penjualan",
      label: "Tanggal Penjualan",
      required: true,
      type: "date",
      example: "2024-01-15",
    },
    {
      key: "Pelanggan",
      label: "Pelanggan",
      required: false,
      type: "text",
      example: "John Doe",
    },
    {
      key: "No. Faktur",
      label: "No. Faktur",
      required: false,
      type: "text",
      example: "FAK-2024-001",
    },
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Quantity",
      label: "Quantity",
      required: true,
      type: "number",
      example: 5,
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Jual",
      label: "Harga Jual",
      required: true,
      type: "currency",
      example: 75000,
    },
    {
      key: "Diskon (%)",
      label: "Diskon (%)",
      required: false,
      type: "number",
      example: 10,
    },
    {
      key: "Diskon (Rp)",
      label: "Diskon (Rp)",
      required: false,
      type: "currency",
      example: 7500,
    },
    {
      key: "PPN (%)",
      label: "PPN (%)",
      required: false,
      type: "number",
      example: 11,
    },
    {
      key: "Harga Grosir",
      label: "Harga Grosir",
      required: false,
      type: "boolean",
      example: "Ya",
    },
    {
      key: "Gudang",
      label: "Gudang",
      required: false,
      type: "text",
      example: "Gudang Utama",
    },
    {
      key: "Memo",
      label: "Memo",
      required: false,
      type: "text",
      example: "Penjualan reguler",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "2024-01-16",
      "Jane Smith",
      "FAK-2024-002",
      "Teh Hijau Organik",
      10,
      "Box",
      45000,
      5,
      2250,
      11,
      "Tidak",
      "Gudang Cabang",
      "Penjualan promosi",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, CELL_STYLES.tableHeader);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 1, c: index });
    if (col.type === "currency" || col.type === "number") {
      applyCellStyle(worksheet, cellRef, {
        ...CELL_STYLES.tableDataEven,
        numFmt:
          col.type === "currency"
            ? NUMBER_FORMATS.currency
            : NUMBER_FORMATS.integer,
      });
    } else {
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Penjualan");

  return workbook;
};

// Create Excel import template for services
export const createServiceImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns for services
  const columns = [
    {
      key: "Nomor Servis",
      label: "Nomor Servis",
      required: false,
      type: "text",
      example: "SRV-25S000001",
    },
    {
      key: "Nama Pelanggan",
      label: "Nama Pelanggan",
      required: true,
      type: "text",
      example: "John Doe",
    },
    {
      key: "Telepon Pelanggan",
      label: "Telepon Pelanggan",
      required: true,
      type: "text",
      example: "081234567890",
    },
    {
      key: "Email Pelanggan",
      label: "Email Pelanggan",
      required: false,
      type: "text",
      example: "<EMAIL>",
    },
    {
      key: "Alamat Pelanggan",
      label: "Alamat Pelanggan",
      required: false,
      type: "text",
      example: "Jl. Contoh No. 123",
    },
    {
      key: "Jenis Perangkat",
      label: "Jenis Perangkat",
      required: true,
      type: "text",
      example: "LAPTOP",
    },
    {
      key: "Merek Perangkat",
      label: "Merek Perangkat",
      required: true,
      type: "text",
      example: "Dell",
    },
    {
      key: "Model Perangkat",
      label: "Model Perangkat",
      required: true,
      type: "text",
      example: "Inspiron 15",
    },
    {
      key: "Serial Number",
      label: "Serial Number",
      required: false,
      type: "text",
      example: "DL123456789",
    },
    {
      key: "Deskripsi Masalah",
      label: "Deskripsi Masalah",
      required: true,
      type: "text",
      example: "Laptop tidak bisa menyala",
    },
    {
      key: "Estimasi Biaya",
      label: "Estimasi Biaya",
      required: false,
      type: "currency",
      example: 500000,
    },
    {
      key: "Catatan Diagnosis",
      label: "Catatan Diagnosis",
      required: false,
      type: "text",
      example: "Perlu penggantian baterai",
    },
    {
      key: "Catatan Perbaikan",
      label: "Catatan Perbaikan",
      required: false,
      type: "text",
      example: "Baterai sudah diganti",
    },
    {
      key: "Periode Garansi",
      label: "Periode Garansi",
      required: false,
      type: "number",
      example: 30,
    },
    {
      key: "Prioritas",
      label: "Prioritas",
      required: false,
      type: "text",
      example: "MEDIUM",
    },
    {
      key: "Status",
      label: "Status",
      required: false,
      type: "text",
      example: "DITERIMA",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "SRV-25S000002",
      "Jane Smith",
      "081987654321",
      "<EMAIL>",
      "Jl. Merdeka No. 456",
      "DESKTOP",
      "HP",
      "Pavilion 24",
      "HP987654321",
      "Komputer sering restart sendiri",
      750000,
      "Kemungkinan masalah RAM",
      "RAM sudah diganti dengan yang baru",
      45,
      "HIGH",
      "PROSES_MENUNGGU_SPAREPART",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, CELL_STYLES.tableHeader);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 1, c: index });
    if (col.type === "currency" || col.type === "number") {
      applyCellStyle(worksheet, cellRef, {
        ...CELL_STYLES.tableDataEven,
        numFmt:
          col.type === "currency"
            ? NUMBER_FORMATS.currency
            : NUMBER_FORMATS.integer,
      });
    } else {
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Servis");

  return workbook;
};
