import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { getUserSubscription } from "@/lib/subscription";
import {
  canCreateProduct,
  canCreateContact,
  canCreateTransaction,
  canCreateEmployee,
} from "@/lib/subscription-limits";

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return NextResponse.json(
        { error: "Unable to determine user ID" },
        { status: 400 }
      );
    }

    // Get user subscription
    const userSubscription = await getUserSubscription(effectiveUserId);

    // Check all limits
    const [productCheck, contactCheck, transactionCheck, userCheck] = await Promise.all([
      canCreateProduct(effectiveUserId, userSubscription.plan),
      canCreateContact(effectiveUserId, userSubscription.plan),
      canCreateTransaction(effectiveUserId, userSubscription.plan),
      canCreateEmployee(effectiveUserId, userSubscription.plan),
    ]);

    return NextResponse.json({
      products: productCheck,
      contacts: contactCheck,
      transactions: transactionCheck,
      users: userCheck,
      subscription: {
        plan: userSubscription.plan,
        planDetails: userSubscription.planDetails,
        expiryDate: userSubscription.expiryDate,
        isActive: userSubscription.isActive,
      },
    });
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
